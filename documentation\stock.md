# Documentation de l'app stock_Pme

## Introduction
L'application `stock_Pme` gère les mouvements, l'état et la performance du stock pour les PME. Elle centralise la logique métier liée à la gestion des stocks, des alertes, des rapports et des métriques de performance, tout en s'intégrant avec les modules produits, entrepôts et magasins.

---

## Modèles principaux

### 1. MouvementStock
- Suit chaque mouvement de stock (entrée, sortie, transfert, ajustement).
- Champs :
  - `id_mouvement` (UUID, clé primaire)
  - `produit` (FK Produit)
  - `location` (FK Location)
  - `quantite` (entier positif)
  - `type_mouvement` (choix : ENTREE, SORTIE, TRANSFERT, AJUSTEMENT)
  - `date_mouvement` (auto, date/heure)
  - `utilisateur` (nom utilisateur)
  - `notes` (texte optionnel)

### 2. Stock
- Représente l’état du stock pour un produit donné dans un magasin et un entrepôt.
- Champs :
  - `id_stock` (UUID, clé primaire)
  - `quantite_disponible` (entier)
  - `seuil_alerte` (entier)
  - `updatedAt` (date MAJ)
  - `produit` (FK Produit)
  - `location` (FK Location)
  - `magasin` (FK Magasin)
  - `entrepot` (FK Entrepot)
- Méthodes utiles :
  - `ajouterStock(quantity)`
  - `retirerStock(quantity)`
  - `vérifierSeuilAlerte()`
  - `getStockByProduct(productId)`

---

## Serializers
- **StockSerializer** : Sérialise tous les champs du modèle Stock, inclut les détails du produit et du magasin.

---

## Services métiers (`services.py`)
- `verifier_capacite_entrepot(entrepot)` : Capacité max, stock actuel, espace dispo, dépassement.
- `generer_rapport_stock(entrepot)` : Rapport détaillé du stock par entrepôt.
- `executer_mouvement(...)` : Gère un mouvement de stock (entrée/sortie), crée le mouvement, met à jour le stock, gère les erreurs (produit, magasin, entrepôt, location, quantité).

---

## Endpoints API

| Méthode | URL | Vue/API | Description |
|---------|-----|---------|-------------|
| GET | `/entrepots/<uuid:entrepot_id>/capacite/` | CapaciteEntrepotAPIView | Vérifie la capacité d’un entrepôt |
| GET | `/entrepots/<uuid:entrepot_id>/rapport/` | RapportStockAPIView | Génère un rapport de stock pour un entrepôt |
| POST | `/mouvements/` | MouvementStockAPIView | Ajoute un mouvement de stock (entrée/sortie) |
| GET | `/mouvements/` | MouvementStockAPIView | Liste les mouvements de stock (filtrage possible) |
| GET | `/alertes/` | AlerteStockAPIView | Liste les alertes de stock (sous le seuil) |
| GET | `/summary/` | StockSummaryAPIView | Résumé global du stock (quantités, alertes, épuisés) |
| GET | `/performance/` | StockPerformanceAPIView | Métriques de performance système, cache, application |

### Exemples de paramètres (POST /mouvements/)
- `produit_id` (UUID, requis)
- `quantite` (int, requis)
- `type_mouvement` (str, requis, ENTREE/SORTIE)
- `entrepot_id` (UUID, optionnel)
- `magasin_id` (UUID, optionnel)
- `notes` (str, optionnel)

---

## Points de configuration spécifiques
- Middleware additionnel possible (voir `settings.py` local).
- L’app dépend des apps : `produit`, `entrepot`, `magasins`.

---

## Bonnes pratiques & sécurité
- Permissions : toutes les vues nécessitent l’authentification (`IsAuthenticated , isAdmin`).
- Validation stricte des entrées (quantité positive, existence des FK).
- Gestion des erreurs explicite (404, 400, etc).
- Utilisation de caches et d’optimisations si le module `performance` est disponible.

---

## Extension & Personnalisation
- Ajout de signaux personnalisés possible (fichier `signals.py` prêt).
- Possibilité d’étendre les endpoints ou d’ajouter des hooks métiers dans les services.
- Intégration facile avec d’autres modules métiers (produit, entrepôt, magasin).

---

## Références croisées
- **Produit** : `produit.models.Produit`
- **Entrepôt** : `entrepot.models.Entrepot`, `entrepot.models.Location`
- **Magasin** : `magasins.models.Magasin`

---

## Pour aller plus loin
- Voir le code source pour les optimisations de performance (cache, monitoring, etc).
- Ajouter des tests unitaires pour chaque endpoint métier.
- Utiliser la documentation Swagger générée automatiquement pour tester les endpoints. 

# Documentation API - Gestion des Stocks

Cette documentation décrit les principaux endpoints pour la gestion des stocks dans le module `stock_Pme`.

## Sommaire
- [Récupérer tous les stocks](#récupérer-tous-les-stocks)
- [Récupérer les stocks par type de stock](#récupérer-les-stocks-par-type-de-stock)
- [Récupérer les stocks pour un produit particulier](#récupérer-les-stocks-pour-un-produit-particulier)
- [Cas particuliers : produit sans stock](#cas-particuliers-produit-sans-stock)
- [Autres endpoints utiles](#autres-endpoints-utiles)

---

## Récupérer tous les stocks

**GET** `/api/stock_Pme/stocks/`

- Retourne la liste complète de tous les stocks.

**Exemple de réponse :**
```json
[
  {
    "id_stock": "uuid",
    "quantite_disponible": 50,
    "seuil_alerte": 10,
    "produit": "uuid du produit",
    "magasin": "uuid du magasin",
    "entrepot": "uuid de l'entrepot",
    "produit_details": { ... },
    "magasin_details": { ... }
    // ... autres champs
  }
]
```

---

## Récupérer les stocks par type de stock

**GET** `/api/stock_Pme/stocks/by-type/?type_stock=FINIS`

- Filtre les stocks selon le type de stock du produit.
- Types possibles : `FINIS`, `MATIERE`, `EMBALLAGE`, `SEMI_FINI`, `CONSOMMABLE`

**Exemple de requête :**
```
GET /api/stock_Pme/stocks/by-type/?type_stock=FINIS
```

**Exemple de réponse :**
```json
[
  {
    "id_stock": "uuid",
    "quantite_disponible": 20,
    "produit_details": { ... },
    "magasin_details": { ... }
  }
]
```

---

## Récupérer les stocks pour un produit particulier

**GET** `/api/stock_Pme/stocks/by-product/?produit_id=<uuid>`

- Retourne tous les stocks pour un produit donné (par son ID).

**Exemple de requête :**
```
GET /api/stock_Pme/stocks/by-product/?produit_id=123e4567-e89b-12d3-a456-************
```

### Cas 1 : Produit avec stock
```json
[
  {
    "id_stock": "uuid",
    "quantite_disponible": 50,
    "produit_details": { ... },
    "magasin_details": { ... }
  }
]
```

### Cas 2 : Produit sans stock
```json
{
  "produit": {
    "id": "uuid",
    "nom": "Nom du produit",
    "reference": "REF123",
    "type_stock": "FINIS",
    "entreprise": "Nom de l'entreprise"
  },
  "message": "Ce produit n'a pas encore de stock (aucune entrée de stock effectuée)",
  "stocks": []
}
```

### Cas 3 : Produit inexistant
```json
{
  "error": "Produit non trouvé"
}
```

---

## Cas particuliers : produit sans stock
- Un produit peut exister dans la base sans qu'aucune entrée de stock n'ait été faite.
- Dans ce cas, l'API retourne un message explicite et les infos du produit, mais la liste des stocks est vide.

---

## Autres endpoints utiles

- **GET** `/api/stock_Pme/entrepots/<uuid:entrepot_id>/rapport/` : Rapport de stock pour un entrepôt
- **GET** `/api/stock_Pme/alertes/` : Liste des stocks en alerte (quantité sous le seuil)
- **GET** `/api/stock_Pme/summary/` : Résumé global du stock

---

## Notes
- Tous les endpoints sont documentés avec Swagger (drf-yasg).
- Les réponses sont généralement au format JSON.
- Les filtres se font via les paramètres d'URL (`?type_stock=...`, `?produit_id=...`).

---

**Pour toute question ou évolution, voir le code source ou contacter l'équipe technique.** 
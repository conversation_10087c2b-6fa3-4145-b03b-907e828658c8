from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from .models import Entrepot
from .serializers import EntrepotSerializer
from stock_Pme.models import Stock
from produit.models import Produit
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class EntrepotView(APIView):
    @swagger_auto_schema(
        operation_description="Lister tous les entrepôts",
        responses={
            200: openapi.Response(
                description="Liste des entrepôts",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'id_entrepot': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                            'nom': openapi.Schema(type=openapi.TYPE_STRING),
                            'adresse': openapi.Schema(type=openapi.TYPE_STRING),
                            'capacite_stockage': openapi.Schema(type=openapi.TYPE_INTEGER),
                            'statut': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                            'entreprise': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', nullable=True)
                        }
                    )
                )
            ),
            400: "Bad Request"
        }
    )
    def get(self, request):
        entrepots = Entrepot.objects.all()
        serializer = EntrepotSerializer(entrepots, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Créer un nouvel entrepôt",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['nom', 'adresse'],
            properties={
                'nom': openapi.Schema(type=openapi.TYPE_STRING),
                'adresse': openapi.Schema(type=openapi.TYPE_STRING),
                'capacite_max': openapi.Schema(type=openapi.TYPE_INTEGER),
                'description': openapi.Schema(type=openapi.TYPE_STRING)
            }
        ),
        responses={
            201: openapi.Response(
                description="Entrepôt créé avec succès",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request"
        }
    )
    def post(self, request):
        serializer = EntrepotSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class EntrepotDetailView(APIView):
    @swagger_auto_schema(
        operation_description="Récupérer les détails d'un entrepôt spécifique",
        responses={
            200: openapi.Response(
                description="Détails de l'entrepôt",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            404: "Not Found"
        }
    )
    def get(self, request, id):
        try:
            entrepot = Entrepot.objects.get(id_entrepot=id)
            serializer = EntrepotSerializer(entrepot)
            return Response(serializer.data)
        except Entrepot.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Mettre à jour un entrepôt existant",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'nom': openapi.Schema(type=openapi.TYPE_STRING),
                'adresse': openapi.Schema(type=openapi.TYPE_STRING),
                'capacite_max': openapi.Schema(type=openapi.TYPE_INTEGER),
                'responsable': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID)
            }
        ),
        responses={
            200: openapi.Response(
                description="Entrepôt mis à jour",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def put(self, request, id):
        try:
            entrepot = Entrepot.objects.get(id_entrepot=id)
            serializer = EntrepotSerializer(entrepot, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Entrepot.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Supprimer un entrepôt",
        responses={
            204: "No Content",
            404: "Not Found"
        }
    )
    def delete(self, request, id):
        try:
            entrepot = Entrepot.objects.get(id_entrepot=id)
            entrepot.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Entrepot.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

class EntrepotStockView(APIView):
    @swagger_auto_schema(
        operation_description="Récupérer le stock d'un entrepôt spécifique",
        responses={
            200: openapi.Response(
                description="Liste des produits en stock dans l'entrepôt",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'entrepot': openapi.Schema(type=openapi.TYPE_OBJECT),
                        'stock': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'produit': openapi.Schema(type=openapi.TYPE_STRING),
                                    'quantite': openapi.Schema(type=openapi.TYPE_NUMBER),
                                    'unite_mesure': openapi.Schema(type=openapi.TYPE_STRING)
                                }
                            )
                        )
                    }
                )
            ),
            404: "Not Found"
        }
    )
    def get(self, request, id):
        try:
            entrepot = Entrepot.objects.get(id_entrepot=id)
            stock = Stock.objects.filter(entrepot=entrepot)
            
            stock_data = []
            for item in stock:
                stock_data.append({
                    'produit': item.produit.nom,
                    'quantite': item.quantite_disponible,
                    'unite_mesure': item.produit.unite_mesure
                })
            
            return Response({
                'entrepot': EntrepotSerializer(entrepot).data,
                'stock': stock_data
            })
        except Entrepot.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

class RapportStockView(APIView):
    def get(self, request, entrepot_id):
        try:
            entrepot = Entrepot.objects.get(id_entrepot=entrepot_id)
            rapport = entrepot.generer_rapport_stock()
            return Response(rapport)
        except Entrepot.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
from rest_framework import status, viewsets
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from .models import Magasin, LocalisationMagasin
from .serializers import MagasinSerializer, LocalisationMagasinSerializer
from base.views import IsAdmin
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class LocalisationMagasinViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated ]
    queryset = LocalisationMagasin.objects.all()
    serializer_class = LocalisationMagasinSerializer

    @swagger_auto_schema(
        operation_description="Lister toutes les localisations de magasins",
        responses={
            200: openapi.Response(
                description="Liste des localisations",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            400: "Bad Request"
        }
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Créer une nouvelle localisation de magasin",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['adresse', 'ville', 'code_postal', 'pays', 'telephone'],
            properties={
                'adresse': openapi.Schema(type=openapi.TYPE_STRING),
                'ville': openapi.Schema(type=openapi.TYPE_STRING, max_length=100),
                'code_postal': openapi.Schema(type=openapi.TYPE_STRING, max_length=20),
                'pays': openapi.Schema(type=openapi.TYPE_STRING, max_length=100),
                'telephone': openapi.Schema(type=openapi.TYPE_STRING, max_length=15, pattern='^\+?[1-9]\d{1,14}$'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, format='email', nullable=True),
                'actif': openapi.Schema(type=openapi.TYPE_BOOLEAN, default=True)
            }
        ),
        responses={
            201: openapi.Response(
                description="Localisation créée avec succès",
                schema=LocalisationMagasinSerializer
            ),
            400: "Bad Request"
        }
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Récupérer les détails d'une localisation spécifique",
        responses={
            200: LocalisationMagasinSerializer,
            404: "Not Found"
        }
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Mettre à jour une localisation existante",
        request_body=LocalisationMagasinSerializer,
        responses={
            200: LocalisationMagasinSerializer,
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Supprimer une localisation",
        responses={
            204: "No Content",
            404: "Not Found"
        }
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

class MagasinViewSet(viewsets.ModelViewSet): 
    # permission_classes = [IsAuthenticated ]
    queryset = Magasin.objects.all()
    serializer_class = MagasinSerializer

    @swagger_auto_schema(
        operation_description="Lister tous les magasins",
        responses={
            200: openapi.Response(
                description="Liste des magasins",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            400: "Bad Request"
        }
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Créer un nouveau magasin",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['nom', 'entreprise', 'localisation'],
            properties={
                'nom': openapi.Schema(type=openapi.TYPE_STRING, max_length=100),
                'entreprise': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                'responsable_magasin': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', nullable=True),
                'entrepot': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', nullable=True),
                'localisation': openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    required=['adresse', 'ville', 'code_postal', 'pays', 'telephone'],
                    properties={
                        'adresse': openapi.Schema(type=openapi.TYPE_STRING),
                        'ville': openapi.Schema(type=openapi.TYPE_STRING, max_length=100),
                        'code_postal': openapi.Schema(type=openapi.TYPE_STRING, max_length=20),
                        'pays': openapi.Schema(type=openapi.TYPE_STRING, max_length=100),
                        'telephone': openapi.Schema(type=openapi.TYPE_STRING, max_length=15, pattern='^\+?[1-9]\d{1,14}$'),
                        'email': openapi.Schema(type=openapi.TYPE_STRING, format='email', nullable=True),
                        'actif': openapi.Schema(type=openapi.TYPE_BOOLEAN, default=True)
                    }
                ),
                'actif': openapi.Schema(type=openapi.TYPE_BOOLEAN, default=True)
            }
        ),
        responses={
            201: openapi.Response(
                description="Magasin créé avec succès",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request"
        }
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Récupérer les détails d'un magasin spécifique",
        responses={
            200: openapi.Response(
                description="Détails du magasin",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            404: "Not Found"
        }
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Mettre à jour un magasin existant",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'nom': openapi.Schema(type=openapi.TYPE_STRING, max_length=100),
                'entreprise': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                'responsable_magasin': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', nullable=True),
                'entrepot': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', nullable=True),
                'localisation': openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'adresse': openapi.Schema(type=openapi.TYPE_STRING),
                        'ville': openapi.Schema(type=openapi.TYPE_STRING, max_length=100),
                        'code_postal': openapi.Schema(type=openapi.TYPE_STRING, max_length=20),
                        'pays': openapi.Schema(type=openapi.TYPE_STRING, max_length=100),
                        'telephone': openapi.Schema(type=openapi.TYPE_STRING, max_length=15, pattern='^\+?[1-9]\d{1,14}$'),
                        'email': openapi.Schema(type=openapi.TYPE_STRING, format='email', nullable=True),
                        'actif': openapi.Schema(type=openapi.TYPE_BOOLEAN)
                    }
                ),
                'actif': openapi.Schema(type=openapi.TYPE_BOOLEAN)
            }
        ),
        responses={
            200: openapi.Response(
                description="Magasin mis à jour",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Supprimer un magasin",
        responses={
            204: "No Content",
            404: "Not Found"
        }
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @action(detail=True, methods=['post'])
    def ajouter_localisation(self, request, pk=None):
        magasin = self.get_object()
        serializer = LocalisationMagasinSerializer(data=request.data)
        if serializer.is_valid():
            localisation = serializer.save()
            magasin.localisation = localisation
            magasin.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def retirer_localisation(self, request, pk=None):
        magasin = self.get_object()
        if not magasin.localisation:
            return Response(
                {'error': 'Ce magasin n\'a pas de localisation'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        localisation = magasin.localisation
        magasin.localisation = None
        magasin.save()
        localisation.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['get'])
    def rapport_ventes(self, request, pk=None):
        magasin = self.get_object()
        rapport = magasin.generer_rapport_ventes()
        return Response(rapport)
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from .models import Client
from .serializers import ClientSerializer
from base.views import IsAdmin, isResponsable_Magasin
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class ClientListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Lister tous les clients",
        responses={
            200: openapi.Response(
                description="Liste des clients",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            400: "Bad Request"
        }
    )
    def get(self, request):
        clients = Client.objects.all()
        serializer = ClientSerializer(clients, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Créer un nouveau client",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'nom': openapi.Schema(type=openapi.TYPE_STRING),
                'prenom': openapi.Schema(type=openapi.TYPE_STRING),
                'telephone': openapi.Schema(type=openapi.TYPE_STRING),
                'email': openapi.Schema(type=openapi.TYPE_STRING),
                'adresse': openapi.Schema(type=openapi.TYPE_STRING)
            }
        ),
        responses={
            201: openapi.Response(
                description="Client créé avec succès",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'client': openapi.Schema(type=openapi.TYPE_OBJECT),
                        'message': openapi.Schema(type=openapi.TYPE_STRING)
                    }
                )
            ),
            400: "Bad Request"
        }
    )
    def post(self, request):
        serializer = ClientSerializer(data=request.data)
        if serializer.is_valid():
            client = serializer.save()
            return Response({
                'client': serializer.data,
                'message': 'Client créé avec succès'
            }, status=status.HTTP_201_CREATED)
        return Response({
            'errors': serializer.errors,
            'message': 'Erreur lors de la création du client'
        }, status=status.HTTP_400_BAD_REQUEST)

class ClientDetailView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Récupérer les détails d'un client spécifique",
        responses={
            200: openapi.Response(
                description="Détails du client",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            404: "Not Found"
        }
    )
    def get(self, request, id):
        try:
            client = Client.objects.get(id_client=id)
            serializer = ClientSerializer(client)
            return Response(serializer.data)
        except Client.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Mettre à jour un client existant",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'nom': openapi.Schema(type=openapi.TYPE_STRING),
                'prenom': openapi.Schema(type=openapi.TYPE_STRING),
                'telephone': openapi.Schema(type=openapi.TYPE_STRING),
                'email': openapi.Schema(type=openapi.TYPE_STRING),
                'adresse': openapi.Schema(type=openapi.TYPE_STRING)
            }
        ),
        responses={
            200: openapi.Response(
                description="Client mis à jour",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def put(self, request, id):
        try:
            client = Client.objects.get(id_client=id)
            serializer = ClientSerializer(client, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Client.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Supprimer un client",
        responses={
            204: "No Content",
            404: "Not Found"
        }
    )
    def delete(self, request, id):
        try:
            client = Client.objects.get(id_client=id)
            client.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Client.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
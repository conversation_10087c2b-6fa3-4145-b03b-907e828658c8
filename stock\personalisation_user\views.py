from rest_framework.permissions import IsAuthenticated
from .permissions import IsAdmin
from rest_framework.views import APIView
from rest_framework.response import Response
from base.models import User
from base.serializer import UserSerializer
from rest_framework import status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

#liste utilisateur 
class List_User_View(APIView):
    # permission_classes = [IsAuthenticated ]

    @swagger_auto_schema(
        operation_description="Lister tous les utilisateurs",
        responses={200: UserSerializer(many=True)}
    )
    def get(self, request) :
        users = User.objects.all()
        serializer = UserSerializer(users, many=True, context={'request': request})
        return Response(serializer.data) 
       

#utilisateur specifique
class GetUserView(APIView):
    # permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    @swagger_auto_schema(
        operation_description="Récupérer l'utilisateur connecté",
        responses={200: UserSerializer()}
    )
    def get(self, request):
        try:
            user = request.user  
            serializer = UserSerializer(user, context={'request': request})
            return Response(serializer.data)
        except User.DoesNotExist:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)
    
     # modification de users et photo profil   
    @swagger_auto_schema(
        operation_description="Modifier l'utilisateur connecté (partiel)",
        request_body=UserSerializer,
        responses={200: UserSerializer(), 400: "Bad Request"}
    )
    def patch(self , request):
        user = request.user
        serializer = UserSerializer(user, data=request.data, partial=True)
        data = request.data.dict() if hasattr(request.data, 'dict') else request.data
        # Gestion spécifique du fichier
        if 'avatar' in request.FILES:
            data['avatar'] = request.FILES['avatar']
        serializer = UserSerializer(
            user,
            data=data,
            partial=True,
            context={'request': request}
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# filtrer utilisateur par role 
class Filter_Users_By_Role(APIView):
    permission_classes = [IsAuthenticated , IsAdmin]

    @swagger_auto_schema(
        operation_description="Filtrer les utilisateurs par rôle",
        manual_parameters=[openapi.Parameter('role', openapi.IN_QUERY, description="Rôle à filtrer", type=openapi.TYPE_STRING)],
        responses={200: UserSerializer(many=True)}
    )
    def get(self, request) :
        role = request.query_params.get('role', None)   # Récupérer le paramètre 'role' dans l'URL 
        if role :
            users = User.objects.filter(role=role) #filtrer par role
        else :
            users = User.objects.all() #si pas de role, tous les utilisateurs
        serializer = UserSerializer(users, many=True, context={'request': request})
        return Response(serializer.data)

class GetUserByIdView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    @swagger_auto_schema(
        operation_description="Récupérer un utilisateur par son ID",
        manual_parameters=[openapi.Parameter('user_id', openapi.IN_PATH, description="ID utilisateur", type=openapi.TYPE_STRING)],
        responses={200: UserSerializer(), 404: "Utilisateur non trouvé"}
    )
    def get(self, request, user_id):
        try:
            user = User.objects.get(id_utilisateur=user_id)
            serializer = UserSerializer(user, context={'request': request})
            return Response(serializer.data)
        except User.DoesNotExist:
            return Response({"error": "Utilisateur non trouvé"}, status=status.HTTP_404_NOT_FOUND)
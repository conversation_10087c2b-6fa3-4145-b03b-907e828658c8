from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from .models import Fournisseur
from .serializers import FournisseurSerializer, CreateFournisseurSerializer
from base.views import IsAdmin
from achat_magasin.serializers import AchatMagasinSerializer
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class FournisseurListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Lister tous les fournisseurs",
        responses={
            200: openapi.Response(
                description="Liste des fournisseurs",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            400: "Bad Request"
        }
    )
    def get(self, request):
        """Lister tous les fournisseurs"""
        fournisseurs = Fournisseur.objects.all()
        serializer = FournisseurSerializer(fournisseurs, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Créer un nouveau fournisseur",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['user', 'categorie_produits'],
            properties={
                'user': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                'reference_fiscale': openapi.Schema(type=openapi.TYPE_STRING, max_length=50, nullable=True),
                'categorie_produits': openapi.Schema(type=openapi.TYPE_STRING, max_length=100),
                'statut': openapi.Schema(type=openapi.TYPE_STRING, enum=['ACTIF', 'INACTIF'], default='ACTIF'),
                'notes': openapi.Schema(type=openapi.TYPE_STRING)
            }
        ),
        responses={
            201: openapi.Response(
                description="Fournisseur créé avec succès",
                schema=FournisseurSerializer
            ),
            400: "Bad Request"
        }
    )
    def post(self, request):
        """Créer un nouveau fournisseur"""
        serializer = FournisseurSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class FournisseurDetailView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Récupérer les détails d'un fournisseur spécifique",
        responses={
            200: openapi.Response(
                description="Détails du fournisseur",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            404: "Not Found"
        }
    )
    def get(self, request, id):
        try:
            fournisseur = Fournisseur.objects.get(id_fournisseur=id)
            serializer = FournisseurSerializer(fournisseur)
            return Response(serializer.data)
        except Fournisseur.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Mettre à jour un fournisseur existant",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                'reference_fiscale': openapi.Schema(type=openapi.TYPE_STRING, max_length=50, nullable=True),
                'categorie_produits': openapi.Schema(type=openapi.TYPE_STRING, max_length=100),
                'statut': openapi.Schema(type=openapi.TYPE_STRING, enum=['ACTIF', 'INACTIF']),
                'notes': openapi.Schema(type=openapi.TYPE_STRING)
            }
        ),
        responses={
            200: openapi.Response(
                description="Fournisseur mis à jour",
                schema=FournisseurSerializer
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def put(self, request, id):
        try:
            fournisseur = Fournisseur.objects.get(id_fournisseur=id)
            serializer = FournisseurSerializer(fournisseur, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Fournisseur.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Supprimer un fournisseur",
        responses={
            204: "No Content",
            404: "Not Found"
        }
    )
    def delete(self, request, id):
        try:
            fournisseur = Fournisseur.objects.get(id_fournisseur=id)
            fournisseur.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Fournisseur.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    def creer_commande(self, request, id):
        try:
            fournisseur = Fournisseur.objects.get(id=id)
            data = request.data
            achat = fournisseur.creer_commande(data)
            return Response(AchatMagasinSerializer(achat).data, status=status.HTTP_201_CREATED)
        except Fournisseur.DoesNotExist:
            return Response({"error": "Fournisseur non trouvé"}, status=status.HTTP_404_NOT_FOUND)

    def mettre_a_jour_statut(self, request, id):
        try:
            fournisseur = Fournisseur.objects.get(id=id)
            new_statut = request.data.get('statut')
            fournisseur.mettre_a_jour_statut(new_statut)
            return Response(FournisseurSerializer(fournisseur).data)
        except Fournisseur.DoesNotExist:
            return Response({"error": "Fournisseur non trouvé"}, status=status.HTTP_404_NOT_FOUND)
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from .models import Inventaire, DetailInventaire
from produit.models import Produit
from .serializers import InventaireSerializer, DetailInventaireSerializer
from base.views import IsAdmin
from django.core.exceptions import ValidationError
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class InventaireListCreateView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    @swagger_auto_schema(
        operation_description="Lister tous les inventaires",
        responses={
            200: openapi.Response(
                description="Liste des inventaires",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            400: "Bad Request"
        }
    )
    def get(self, request):
        inventaires = Inventaire.objects.all()
        serializer = InventaireSerializer(inventaires, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Créer un nouvel inventaire",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['entrepot', 'magasin', 'responsable'],
            properties={
                'entrepot': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'magasin': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'responsable': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'type_inventaire': openapi.Schema(type=openapi.TYPE_STRING, enum=['COMPLET', 'PARTIEL']),
                'date_planification': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE)
            }
        ),
        responses={
            201: openapi.Response(
                description="Inventaire créé avec succès",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request"
        }
    )
    def post(self, request):
        serializer = InventaireSerializer(data=request.data)
        if serializer.is_valid():
            inventaire = Inventaire.demarrerInventaire(
                id_entrepot=request.data.get('entrepot'),
                id_magasin=request.data.get('magasin'),
                id_responsable=request.data.get('responsable'),
                type_inventaire=request.data.get('type_inventaire', 'COMPLET')
            )
            if 'date_planification' in request.data:
                inventaire.date_planification = request.data['date_planification']
                inventaire.save()
            return Response(InventaireSerializer(inventaire).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class InventaireDetailView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    @swagger_auto_schema(
        operation_description="Récupérer les détails d'un inventaire spécifique",
        responses={
            200: openapi.Response(
                description="Détails de l'inventaire",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            404: "Not Found"
        }
    )
    def get(self, request, id):
        try:
            inventaire = Inventaire.objects.get(id_inventaire=id)
            serializer = InventaireSerializer(inventaire)
            return Response(serializer.data)
        except Inventaire.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Mettre à jour un inventaire existant",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'entrepot': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'magasin': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'responsable': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'type_inventaire': openapi.Schema(type=openapi.TYPE_STRING, enum=['COMPLET', 'PARTIEL']),
                'date_planification': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE)
            }
        ),
        responses={
            200: openapi.Response(
                description="Inventaire mis à jour",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def put(self, request, id):
        try:
            inventaire = Inventaire.objects.get(id_inventaire=id)
            serializer = InventaireSerializer(inventaire, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Inventaire.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Supprimer un inventaire",
        responses={
            204: "No Content",
            404: "Not Found"
        }
    )
    def delete(self, request, id):
        try:
            inventaire = Inventaire.objects.get(id_inventaire=id)
            inventaire.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Inventaire.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

class InventaireTerminerView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    @swagger_auto_schema(
        operation_description="Terminer un inventaire",
        responses={
            200: openapi.Response(
                description="Inventaire terminé",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def post(self, request, id):
        try:
            inventaire = Inventaire.objects.get(id_inventaire=id)
            inventaire.terminerInventaire()
            return Response(InventaireSerializer(inventaire).data)
        except Inventaire.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class DetailInventaireCreateView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    @swagger_auto_schema(
        operation_description="Ajouter un produit à l'inventaire",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['produit', 'quantite_reelle'],
            properties={
                'produit': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'quantite_reelle': openapi.Schema(type=openapi.TYPE_NUMBER, minimum=0)
            }
        ),
        responses={
            201: openapi.Response(
                description="Détail d'inventaire créé",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def post(self, request, id_inventaire):
        try:
            inventaire = Inventaire.objects.get(id_inventaire=id_inventaire)
            detail = inventaire.ajouterProduitInventaire(
                id_produit=request.data.get('produit'),
                quantite_comptée=request.data.get('quantite_reelle')
            )
            return Response(DetailInventaireSerializer(detail).data, status=status.HTTP_201_CREATED)
        except Inventaire.DoesNotExist:
            return Response({"error": "Inventaire non trouvé"}, status=status.HTTP_404_NOT_FOUND)
        except Produit.DoesNotExist:
            return Response({"error": "Produit non trouvé"}, status=status.HTTP_404_NOT_FOUND)

class DetailInventaireUpdateView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    @swagger_auto_schema(
        operation_description="Mettre à jour les détails d'un produit dans l'inventaire",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['quantite_reelle'],
            properties={
                'quantite_reelle': openapi.Schema(type=openapi.TYPE_NUMBER, minimum=0)
            }
        ),
        responses={
            200: openapi.Response(
                description="Détail d'inventaire mis à jour",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def put(self, request, id_inventaire, id_detail):
        try:
            detail = DetailInventaire.objects.get(
                id_detail=id_detail,
                inventaire_id=id_inventaire
            )
            detail.mettreAJourQuantite(request.data.get('quantite_reelle'))
            return Response(DetailInventaireSerializer(detail).data)
        except DetailInventaire.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class InventaireRapportView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    @swagger_auto_schema(
        operation_description="Générer un rapport d'inventaire",
        responses={
            200: openapi.Response(
                description="Rapport d'inventaire",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'inventaire': openapi.Schema(type=openapi.TYPE_OBJECT),
                        'details': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(type=openapi.TYPE_OBJECT)
                        ),
                        'ecarts': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'produit': openapi.Schema(type=openapi.TYPE_STRING),
                                    'quantite_theorique': openapi.Schema(type=openapi.TYPE_NUMBER),
                                    'quantite_reelle': openapi.Schema(type=openapi.TYPE_NUMBER),
                                    'ecart': openapi.Schema(type=openapi.TYPE_NUMBER)
                                }
                            )
                        )
                    }
                )
            ),
            404: "Not Found"
        }
    )
    def get(self, request, id):
        try:
            inventaire = Inventaire.objects.get(id_inventaire=id)
            details = DetailInventaire.objects.filter(inventaire=inventaire)
            
            ecarts = []
            for detail in details:
                ecart = detail.quantite_reelle - detail.quantite_theorique
                if ecart != 0:
                    ecarts.append({
                        'produit': detail.produit.nom,
                        'quantite_theorique': detail.quantite_theorique,
                        'quantite_reelle': detail.quantite_reelle,
                        'ecart': ecart
                    })
            
            return Response({
                'inventaire': InventaireSerializer(inventaire).data,
                'details': DetailInventaireSerializer(details, many=True).data,
                'ecarts': ecarts
            })
        except Inventaire.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)